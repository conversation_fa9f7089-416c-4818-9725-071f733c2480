package notice

import (
	"assistantdeskgo/api/dataproxy"
	"assistantdeskgo/api/mercury"
	"assistantdeskgo/defines"
	"assistantdeskgo/dto/dtonotice"
	"assistantdeskgo/helpers"
	"assistantdeskgo/middleware"
	"assistantdeskgo/models/notice"
	"assistantdeskgo/utils"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.zuoyebang.cc/fwyybase/fwyylibs/fwyyutils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

const (
	// 缓存key前缀
	NoticeCacheKeyPrefix = "assistantdeskgo_user_notice"
	// 缓存超时时间
	NoticeCacheTimeout = 300
)

// GetUnReadNoticeList 获取当前用户的未读通知列表（顶部轮播栏）
func GetUnReadNoticeList(ctx *gin.Context) (rsp []dtonotice.UserNoticeRsp, err error) {
	rsp = []dtonotice.UserNoticeRsp{}
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	// 有缓存读缓存
	cacheKey := getCacheKey(userInfo.UserId)
	cacheBytes, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err == nil && cacheBytes != nil {
		cacheStr := string(cacheBytes)
		if err = json.Unmarshal([]byte(cacheStr), &rsp); err == nil {
			return
		}
		zlog.Warnf(ctx, "GetUnReadNoticeList unmarshal cache fail, key: %s, cache: %s, err: %+v",
			cacheKey, cacheStr, err)
	}

	unReadNoticeList, err := notice.SystemNoticeUserDao.ListTopUnReadNotice(ctx, userInfo.UserId, nil)
	if err != nil {
		return
	}

	for _, snu := range unReadNoticeList {
		sn, err := notice.SystemNoticeDao.GetById(ctx, snu.NoticeId, nil)
		if err != nil {
			return nil, err
		}

		noticeStatistics, err := getUserNoticeStatistics(ctx, *sn, true)
		if err != nil {
			return nil, err
		}

		isLike, err := getNoticeUserIsLike(ctx, sn.Id, cast.ToInt64(userInfo.UserId))
		if err != nil {
			return nil, err
		}

		userNotice := dtonotice.UserNoticeRsp{
			Id:            sn.Id,
			Title:         sn.Title,
			ClassID:       sn.ClassID,
			ProfilePhoto:  sn.ProfilePhoto,
			AvatarType:    sn.AvatarType,
			AvatarColor:   sn.AvatarColor,
			CoverImage:    sn.CoverImage,
			BottomImageId: sn.BottomImageId,
			Abstract:      sn.Abstract,
			PublishTime:   sn.PublishTime,
			Statistics:    noticeStatistics,
			IsLike:        isLike,
			IsNew:         snu.LastReadTime == 0,
		}

		rsp = append(rsp, userNotice)
	}

	// 加缓存
	cacheData, err := json.Marshal(rsp)
	if err != nil {
		zlog.Warnf(ctx, "GetUnReadNoticeList marshal data fail, err: %v", err)
	} else {
		err = fwyyutils.RunWithRetry(func(idx int) error {
			return helpers.RedisClient.SetEx(ctx, cacheKey, string(cacheData), NoticeCacheTimeout)
		})
		if err != nil {
			zlog.Warnf(ctx, "GetUnReadNoticeList set cache fail, key: %s, err: %v", cacheKey, err)
		}
	}

	return
}

// GetTopNoticeList 获取当前用户的最新通知列表（顶部轮播栏）
func GetTopNoticeList(ctx *gin.Context) (rsp []dtonotice.UserNoticeRsp, err error) {
	rsp = []dtonotice.UserNoticeRsp{}
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	// 有缓存读缓存
	cacheKey := getCacheKey(userInfo.UserId)
	cacheBytes, err := helpers.RedisClient.Get(ctx, cacheKey)
	if err == nil && cacheBytes != nil {
		cacheStr := string(cacheBytes)
		if err = json.Unmarshal([]byte(cacheStr), &rsp); err == nil {
			return
		}
		zlog.Warnf(ctx, "GetUnReadNoticeList unmarshal cache fail, key: %s, cache: %s, err: %+v",
			cacheKey, cacheStr, err)
	}

	topNoticeList, err := notice.SystemNoticeUserDao.ListTopNotice(ctx, userInfo.UserId, nil)
	if err != nil {
		return
	}

	for _, snu := range topNoticeList {
		sn, err := notice.SystemNoticeDao.GetById(ctx, snu.NoticeId, nil)
		if err != nil {
			return nil, err
		}

		noticeStatistics, err := getUserNoticeStatistics(ctx, *sn, true)
		if err != nil {
			return nil, err
		}

		isLike, err := getNoticeUserIsLike(ctx, sn.Id, cast.ToInt64(userInfo.UserId))
		if err != nil {
			return nil, err
		}

		userNotice := dtonotice.UserNoticeRsp{
			Id:            sn.Id,
			Title:         sn.Title,
			ClassID:       sn.ClassID,
			ProfilePhoto:  sn.ProfilePhoto,
			AvatarType:    sn.AvatarType,
			AvatarColor:   sn.AvatarColor,
			CoverImage:    sn.CoverImage,
			BottomImageId: sn.BottomImageId,
			Abstract:      sn.Abstract,
			PublishTime:   sn.PublishTime,
			Statistics:    noticeStatistics,
			IsLike:        isLike,
		}

		rsp = append(rsp, userNotice)
	}

	// 加缓存
	cacheData, err := json.Marshal(rsp)
	if err != nil {
		zlog.Warnf(ctx, "GetUnReadNoticeList marshal data fail, err: %v", err)
	} else {
		err = fwyyutils.RunWithRetry(func(idx int) error {
			return helpers.RedisClient.SetEx(ctx, cacheKey, string(cacheData), NoticeCacheTimeout)
		})
		if err != nil {
			zlog.Warnf(ctx, "GetUnReadNoticeList set cache fail, key: %s, err: %v", cacheKey, err)
		}
	}

	return
}

// GetNoticeDetail 根据通知ID获取通知详情
func GetNoticeDetail(ctx *gin.Context, noticeId int64) (rsp dtonotice.NoticeDetailRsp, err error) {
	sn, err := notice.SystemNoticeDao.GetById(ctx, noticeId, nil)
	if err != nil {
		return
	}
	rsp.Id = sn.Id
	rsp.ClassID = sn.ClassID
	rsp.PublishTime = sn.PublishTime
	rsp.Title = sn.Title
	rsp.Content = sn.Content
	rsp.VideoAddr = sn.VideoAddr

	// 获取发布者名称
	rsp.PublisherName, err = getUserNameByUid(ctx, sn.PublisherUID)
	if err != nil {
		return
	}

	// 获取通知反馈详情
	rsp.ContentLikeDetail, rsp.VideoLikeDetail, err = getUserFeedbackDetail(ctx, noticeId)
	if err != nil {
		return
	}

	// 获取通知图文反馈统计
	rsp.ContentFeedbackStats, err = getNoticeFeedbackStats(ctx, noticeId, defines.NoticeContentType)
	if err != nil {
		return
	}

	// 获取通知视频反馈统计
	rsp.VideoFeedbackStats, err = getNoticeFeedbackStats(ctx, noticeId, defines.NoticeVideoType)
	if err != nil {
		return
	}

	// 获取通知评论详情
	rsp.CommentDetail, err = getCommentsByNoticeId(ctx, noticeId, false)
	if err != nil {
		return
	}

	// 获取通知评论数量
	rsp.CommentCount, err = getNoticeCommentCount(ctx, noticeId)
	if err != nil {
		return
	}

	return
}

// UserNoticeFeedback 通知点赞点踩
func UserNoticeFeedback(ctx *gin.Context, param dtonotice.UserNoticeFeedbackParam) (result dtonotice.IdRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	now := time.Now().Unix()
	result.Id = param.Id
	if param.Id > 0 {
		// 只允许更新反馈内容
		if len(param.FeedbackContent) > 0 {
			updateMap := map[string]interface{}{
				"feedback_content": param.FeedbackContent,
				"feedback_time":    now,
			}
			err = notice.SystemNoticeUserLikeDao.UpdateById(ctx, param.Id, updateMap, nil)
			if err != nil {
				return
			}
		}
	} else {
		// 检查是否已经点赞/点踩过
		likeList, err := notice.SystemNoticeUserLikeDao.List(ctx, map[string]interface{}{
			"notice_id": param.NoticeId,
			"type":      param.Type, // 1-图文点踩，2-视频点踩
			"uid":       int64(userInfo.UserId),
		}, nil)
		if err != nil {
			return result, err
		}

		if len(*likeList) > 0 {
			return result, fmt.Errorf("已经点赞或点踩过")
		}

		// 新增点赞记录
		systemNoticeUserLike := notice.SystemNoticeUserLike{
			NoticeId:        param.NoticeId,
			Uid:             int64(userInfo.UserId),
			Type:            param.Type,
			FeedbackType:    param.FeedbackType,
			FeedbackContent: param.FeedbackContent,
			FeedbackTime:    now,
			CreateTime:      now,
			UpdateTime:      now,
		}

		result.Id, err = notice.SystemNoticeUserLikeDao.Insert(ctx, systemNoticeUserLike, nil)
		if err != nil {
			return result, err
		}
	}

	return
}

// UserNoticeCommentLike 通知评论点赞/取消点赞
func UserNoticeCommentLike(ctx *gin.Context, param dtonotice.UserNoticeCommentLikeParam) (result dtonotice.CommonRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	now := time.Now().Unix()
	// 检查是否已经点赞过
	conds := map[string]interface{}{
		"notice_id":  param.NoticeId,
		"comment_id": param.CommentId,
		"uid":        int64(userInfo.UserId),
	}
	count, err := notice.SystemNoticeCommentLikeDao.Count(ctx, conds, nil)
	if err != nil {
		return
	}

	if count > 0 {
		// 已点赞，取消点赞
		err = notice.SystemNoticeCommentLikeDao.Delete(ctx, conds, nil)
		if err != nil {
			return
		}
	} else {
		// 未点赞，添加点赞
		commentLike := notice.SystemNoticeCommentLike{
			NoticeId:   param.NoticeId,
			CommentId:  param.CommentId,
			Uid:        int64(userInfo.UserId),
			CreateTime: now,
			UpdateTime: now,
		}
		err = notice.SystemNoticeCommentLikeDao.Insert(ctx, commentLike, nil)
		if err != nil {
			return
		}
	}

	return
}

// GetUserNoticeList 获取用户通知列表
func GetUserNoticeList(ctx *gin.Context, param dtonotice.UserNoticeListParam) (rsp dtonotice.UserNoticeListRsp, err error) {
	rsp.List = []dtonotice.UserNoticeRsp{}
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	// 查询用户通知列表
	noticeList, err := notice.SystemNoticeUserDao.ListLatest90DaysNotice(ctx, userInfo.UserId, nil)
	if err != nil || len(noticeList) == 0 {
		return
	}

	// 创建一个映射，用于快速查找通知的已读状态
	var readCount, unreadCount int64
	noticeReadMap := make(map[int64]bool, len(noticeList))
	noticeIds := make([]int64, len(noticeList))
	for _, item := range noticeList {
		noticeIds = append(noticeIds, item.NoticeId)
		noticeReadMap[item.NoticeId] = item.IsContentRead
	}

	classIds, _ := utils.StringToIntSlice(ctx, param.ClassId)
	notices, err := notice.SystemNoticeDao.GetByIdsAndClassIds(ctx, noticeIds, classIds, nil)
	if err != nil {
		return rsp, err
	}

	for _, sn := range notices {
		// 统计已读/未读数量
		if noticeReadMap[sn.Id] {
			readCount++
		} else {
			unreadCount++
		}

		// 根据参数过滤已读/未读通知
		if param.ReadType != 0 {
			isRead := noticeReadMap[sn.Id]
			if (param.ReadType == defines.NoticeUnReadType && !isRead) || (param.ReadType == defines.NoticeReadType && isRead) {
				continue
			}
		}

		// 获取通知统计信息
		noticeStatistics, err := getUserNoticeStatistics(ctx, sn, true)
		if err != nil {
			return rsp, err
		}

		// 获取用户是否点赞
		isLike, err := getNoticeUserIsLike(ctx, sn.Id, cast.ToInt64(userInfo.UserId))
		if err != nil {
			return rsp, err
		}

		userNotice := dtonotice.UserNoticeRsp{
			Id:            sn.Id,
			Title:         sn.Title,
			ClassID:       sn.ClassID,
			ProfilePhoto:  sn.ProfilePhoto,
			AvatarType:    sn.AvatarType,
			AvatarColor:   sn.AvatarColor,
			CoverImage:    sn.CoverImage,
			BottomImageId: sn.BottomImageId,
			Abstract:      sn.Abstract,
			PublishTime:   sn.PublishTime,
			Statistics:    noticeStatistics,
			IsLike:        isLike,
		}
		rsp.List = append(rsp.List, userNotice)
	}

	// 根据发布时间排序
	sort.Slice(rsp.List, func(i, j int) bool {
		return rsp.List[i].PublishTime > rsp.List[j].PublishTime
	})

	rsp.Read = readCount
	rsp.UnRead = unreadCount
	rsp.Total = readCount + unreadCount

	return
}

// UserNoticeReadLater 通知稍后再看
func UserNoticeReadLater(ctx *gin.Context, param dtonotice.UserNoticeReadParam) (result dtonotice.CommonRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	// 修改通知状态成未读
	updateMap := map[string]interface{}{
		"is_content_read": false,
		"is_video_read":   false,
		"update_time":     time.Now().Unix(),
	}
	err = notice.SystemNoticeUserDao.Update(ctx, param.NoticeId, cast.ToInt64(userInfo.UserId), updateMap, nil)
	if err != nil {
		return
	}

	return
}

// UserNoticeReadTime 修改通知的图文、视频阅读状态和浏览时间
func UserNoticeReadTime(ctx *gin.Context, param dtonotice.UserNoticeReadParam) (result dtonotice.CommonRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	noticeId := param.NoticeId
	uid := cast.ToInt64(userInfo.UserId)

	record, err := notice.SystemNoticeUserDao.GetOne(ctx, noticeId, uid, nil)
	if err != nil {
		return
	}

	// 构建更新数据
	updateMap := map[string]interface{}{
		"last_read_time": time.Now().Unix(),
	}
	switch param.Type {
	case defines.NoticeContentType:
		updateMap["is_content_read"] = true
		updateMap["content_read_time"] = record.ContentReadTime + param.ReadTime
	case defines.NoticeVideoType:
		updateMap["is_video_read"] = true
		updateMap["video_read_time"] = record.VideoReadTime + param.ReadTime
	}

	err = notice.SystemNoticeUserDao.Update(ctx, noticeId, uid, updateMap, nil)
	if err != nil {
		return
	}

	return
}

// UserNoticeReadAll 一键已读
func UserNoticeReadAll(ctx *gin.Context) (result dtonotice.CommonRsp, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	// 修改通知状态成已读
	updateMap := map[string]interface{}{
		"is_content_read": true,
		"is_video_read":   true,
		"update_time":     time.Now().Unix(),
	}
	err = notice.SystemNoticeUserDao.UpdateByUid(ctx, cast.ToInt64(userInfo.UserId), updateMap, nil)
	if err != nil {
		return
	}

	return
}

func getUserInfoByUid(ctx *gin.Context, uid int64) (staff dataproxy.StaffInfo, err error) {
	staff = dataproxy.StaffInfo{}
	if uid == 0 {
		return
	}

	// 调用 GetStaffInfoListByFilter 获取用户信息
	rsp, err := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
		StaffUid: []int64{uid},
	})
	if err != nil {
		return
	}

	if len(rsp.List) == 0 {
		err = fmt.Errorf("用户不存在")
		return
	}

	return rsp.List[0], err
}

func getUserNameByUid(ctx *gin.Context, uid int64) (name string, err error) {
	userInfo, err := getUserInfoByUid(ctx, uid)
	if err != nil {
		return
	}
	return userInfo.StaffName, nil
}

// 批量获取用户信息（分批处理，每批最多 1000）
func getUserNameMapByUids(ctx *gin.Context, uids []int64) (userNameMap map[int64]string, err error) {
	userNameMap = make(map[int64]string)
	if len(uids) > 0 {
		const chunkSize = 1000 // 定义每批查询的最大数量
		totalUids := len(uids)

		for i := 0; i < totalUids; i += chunkSize {
			end := i + chunkSize
			if end > totalUids {
				end = totalUids
			}
			chunkUids := uids[i:end] // 获取当前批次的 uids

			// 调用接口获取当前批次的用户信息
			staffInfoRsp, chunkErr := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
				StaffUid: chunkUids,
			})

			if chunkErr != nil {
				zlog.Errorf(ctx, "getUserNameMapByUids: Failed to batch get user info for chunk (uids %d-%d): %v", i, end-1, chunkErr)
				continue
			}

			// 将查询到的用户信息添加到 map 中
			if staffInfoRsp != nil {
				for _, staff := range staffInfoRsp.List {
					userNameMap[staff.StaffUid] = staff.StaffName
				}
			}
		}
	}
	return
}

// 获取通知的点赞状态和已读用户列表
func getNoticeUserIsLike(ctx *gin.Context, noticeId int64, currentUid int64) (isLike bool, err error) {
	// 1. 获取当前用户是否对内容点赞
	likeList, err := notice.SystemNoticeUserLikeDao.List(ctx, map[string]interface{}{
		"notice_id":     noticeId,
		"uid":           currentUid,
		"type":          defines.NoticeContentType,
		"feedback_type": defines.NoticeFeedbackLikeType,
	}, nil)
	if err != nil {
		return
	}
	isLike = len(*likeList) > 0

	return
}

// 根据uids，保存通知接收者
func saveNoticeReceiverByUids(ctx *gin.Context, noticeId int64, uids []int64) (err error) {
	if len(uids) == 0 {
		return
	}

	var noticeUserList []notice.SystemNoticeUser
	for _, uid := range uids {
		noticeUser := notice.SystemNoticeUser{
			NoticeId:   noticeId,
			Uid:        uid,
			CreateTime: time.Now().Unix(),
			UpdateTime: time.Now().Unix(),
		}
		noticeUserList = append(noticeUserList, noticeUser)
	}

	var batchSize = 1000
	// 插入通知接收者，若之前删除过，则更新
	err = notice.SystemNoticeUserDao.BatchInsert(ctx, noticeUserList, batchSize, nil)
	if err != nil {
		return
	}

	// 更新之前可能删除过的用户点赞、评论、评论点赞
	err = notice.SystemNoticeCommentLikeDao.UpdateDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return
	}

	err = notice.SystemNoticeCommentDao.BatchUpdateDeleted(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return
	}

	err = notice.SystemNoticeUserLikeDao.BatchUpdateDeleted(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return
	}

	return
}

func getCacheKey(uId int) string {
	return fmt.Sprintf("%s_%d", NoticeCacheKeyPrefix, uId)
}

// 根据通知范围，保存通知接收者
func saveNoticeReceiver(ctx *gin.Context, noticeId int64, receiverScope dtonotice.ReceiverScopeParam) (err error) {
	uids, err := getUidsByScope(ctx, receiverScope)
	if err != nil {
		return
	}
	err = saveNoticeReceiverByUids(ctx, noticeId, uids)
	if err != nil {
		return
	}
	return
}

// 根据通知范围，新增或者删除通知接收者
func updateNoticeReceiver(ctx *gin.Context, noticeId int64, receiverScope dtonotice.ReceiverScopeParam) (err error) {
	list, err := notice.SystemNoticeUserDao.List(ctx, map[string]interface{}{"notice_id": noticeId}, nil)
	if err != nil {
		return
	}

	uids, err := getUidsByScope(ctx, receiverScope)
	if err != nil {
		return
	}

	var addUids, delUids []int64

	mapOri := make(map[int64]struct{})
	for _, item := range list {
		mapOri[item.Uid] = struct{}{}
	}

	// 查找要新增通知的用户
	for _, item := range uids {
		if _, found := mapOri[item]; !found {
			addUids = append(addUids, item)
		}
	}

	mapNew := make(map[int64]struct{})
	for _, item := range uids {
		mapNew[item] = struct{}{}
	}

	// 查找要删除通知的用户
	for _, item := range list {
		if _, found := mapNew[item.Uid]; !found {
			delUids = append(delUids, item.Uid)
		}
	}

	err = saveNoticeReceiverByUids(ctx, noticeId, addUids)
	if err != nil {
		return
	}

	err = deleteNoticeReceiverByUids(ctx, noticeId, delUids)
	if err != nil {
		return
	}

	return
}

// 根据通知ID删除通知接收者
func deleteNoticeReceiver(ctx *gin.Context, tx *gorm.DB, noticeId int64) (err error) {
	uids, err := notice.SystemNoticeUserDao.GetUids(ctx, noticeId, tx)
	if err != nil {
		return
	}
	err = deleteNoticeReceiverByUids(ctx, noticeId, uids)
	if err != nil {
		return
	}
	return
}

// 根据uids，删除通知接收者
func deleteNoticeReceiverByUids(ctx *gin.Context, noticeId int64, uids []int64) (err error) {
	if len(uids) == 0 {
		return
	}

	var batchSize = 1000
	// 删除通知和用户关联
	err = notice.SystemNoticeUserDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return
	}

	// 删除用户评论和用户点赞关联
	err = notice.SystemNoticeCommentLikeDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return
	}

	// 删除通知和用户评论关联
	err = notice.SystemNoticeCommentDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return
	}

	// 删除通知和用户点赞关联
	err = notice.SystemNoticeUserLikeDao.BatchDeleteByUids(ctx, noticeId, uids, batchSize, nil)
	if err != nil {
		return
	}

	return
}

// 根据通知范围，获取用户uid列表
func getUidsByScope(ctx *gin.Context, param dtonotice.ReceiverScopeParam) ([]int64, error) {
	var uidList []int64
	var groupIds []int64
	// 筛选员工
	if param.Scope == defines.SystemNoticeScopeFilter {
		// 根据groupId进行筛选
		if len(param.ScopeGroup) > 0 {
			split := strings.Split(param.ScopeGroup, ",")
			for _, str := range split {
				num := cast.ToInt64(strings.TrimSpace(str))
				groupIds = append(groupIds, num)
			}
		}

		// 根据uid进行筛选
		if len(param.ScopeUID) > 0 {
			split := strings.Split(param.ScopeUID, ",")
			for _, str := range split {
				num := cast.ToInt64(strings.TrimSpace(str))
				uidList = append(uidList, num)
			}
		}
	}
	// 全体员工：当前只根据特定组织进行筛选
	if param.Scope == defines.SystemNoticeScopeAll {
		var config mercury.NoticeAllScope
		if err := mercury.GetConfigForJson(ctx, mercury.ConfigKeyForNoticeScope, mercury.DefaultExpireTime, &config); err != nil {
			zlog.Warnf(ctx, "[GetDefaultDelayTime] get mercury config failed, err: %+v", err)
			return nil, err
		}
		groupIds = append(groupIds, config.GroupIds...)
	}

	// 根据groupId获取用户uid列表
	if len(groupIds) > 0 {
		// 使用 GetStaffInfoListByFilter 获取组织下的用户
		rsp, err := dataproxy.GetStaffInfoListByFilter(ctx, dataproxy.StaffInfoListReq{
			GroupId:           groupIds,
			StaffType:         []int64{1}, // 只获取正式员工
			StaffStatus:       []int64{1}, // 只获取在职员工
			NeedGroupChildren: 1,
		})
		if err != nil {
			return nil, err
		}
		for _, staff := range rsp.List {
			uidList = append(uidList, staff.StaffUid)
		}
	}

	// 合并结果集并去重
	unique := make(map[int64]struct{})
	for _, v := range uidList {
		unique[v] = struct{}{}
	}
	var uniqueList []int64
	for key := range unique {
		uniqueList = append(uniqueList, key)
	}
	return uniqueList, nil
}

// 获取通知反馈详情
func getUserFeedbackDetail(ctx *gin.Context, noticeId int64) (contentFeedback *dtonotice.UserNoticeFeedback, videoFeedback *dtonotice.UserNoticeFeedback, err error) {
	userInfo, err := middleware.GetLoginUserInfo(ctx)
	if err != nil {
		return
	}

	// 获取当前用户的反馈记录
	userLikeList, err := notice.SystemNoticeUserLikeDao.List(ctx, map[string]interface{}{
		"notice_id": noticeId,
		"uid":       cast.ToInt64(userInfo.UserId),
	}, nil)
	if err != nil {
		return
	}

	// 设置用户反馈详情
	for _, v := range *userLikeList {
		feedbackDetail := dtonotice.UserNoticeFeedback{
			Id:              v.Id,
			FeedbackType:    v.FeedbackType,
			FeedbackContent: v.FeedbackContent,
			FeedbackTime:    v.FeedbackTime,
		}
		if v.Type == defines.NoticeContentType {
			contentFeedback = &feedbackDetail
		}
		if v.Type == defines.NoticeVideoType {
			videoFeedback = &feedbackDetail
		}
	}

	return
}
