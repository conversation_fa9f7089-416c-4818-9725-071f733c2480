package router

import (
	"assistantdeskgo/controllers/api/assistant"
	"assistantdeskgo/controllers/backend/accompany"
	"assistantdeskgo/controllers/backend/bailing/aimessage"
	"assistantdeskgo/controllers/backend/bailing/interview"
	"assistantdeskgo/controllers/backend/csv"
	"assistantdeskgo/controllers/backend/deer"
	"assistantdeskgo/controllers/backend/deskviewdetail"
	"assistantdeskgo/controllers/backend/exercisenote"
	"assistantdeskgo/controllers/backend/feedbackv2"
	"assistantdeskgo/controllers/backend/laxintagfixtool"
	"assistantdeskgo/controllers/backend/leads"
	"assistantdeskgo/controllers/backend/lesson"
	"assistantdeskgo/controllers/backend/message"
	"assistantdeskgo/controllers/backend/message/mergecourse"
	"assistantdeskgo/controllers/backend/notice"
	"assistantdeskgo/controllers/backend/notifyignore"
	"assistantdeskgo/controllers/backend/preclass"
	"assistantdeskgo/controllers/backend/public"
	"assistantdeskgo/controllers/backend/relation"
	"assistantdeskgo/controllers/backend/sailorscore"
	"assistantdeskgo/controllers/backend/satisfaction"
	"assistantdeskgo/controllers/backend/scenetouch"
	"assistantdeskgo/controllers/backend/sop"
	"assistantdeskgo/controllers/backend/student"
	"assistantdeskgo/controllers/backend/template/groupposter"
	"assistantdeskgo/controllers/backend/touchmessagegroup"
	"assistantdeskgo/controllers/backend/wxmessage"
	"assistantdeskgo/controllers/http/wxmass"
	"assistantdeskgo/middleware"

	"github.com/gin-gonic/gin"
)

func Backend(engine *gin.Engine) {
	router := engine.Group("/assistantdeskgo/", middleware.Recover)

	router.Use(middleware.AuthCheck())

	assistantRouter := router.Group("/", middleware.SelectBusiness)

	sailorGroup := router.Group("/sailor")
	{
		sailorGroup.GET("score/listinit", sailorscore.ListInit)
		sailorGroup.Any("score/list", sailorscore.List)
		sailorGroup.GET("score/export", sailorscore.Export)
		sailorGroup.GET("score/info", sailorscore.Info)
		sailorGroup.GET("score/detail", sailorscore.Detail)
		sailorGroup.POST("score/doexcelbytaskid", sailorscore.DoExcelByTaskId) // 后门接口
		sailorGroup.POST("score/updateextrainfo", sailorscore.UpdateExtraInfo) // 后门接口
	}

	// csv文件上传管理
	csvGroup := router.Group("/csv")
	{
		csvGroup.POST("/upload", csv.UploadSailorScore)
		csvGroup.GET("/canupload", csv.CanUploadSailorScore)
		csvGroup.POST("/list", csv.List)
	}

	feedbackV2Group := assistantRouter.Group("/studyfeedbackv2")
	{
		feedbackV2Group.POST("/data", feedbackv2.FeedbackData)
		feedbackV2Group.GET("/lessoninfo", feedbackv2.LessonInfo)
	}

	// 消息组
	touchMessageGroupGroup := assistantRouter.Group("/touchmessagegroup")
	{
		touchMessageGroupGroup.POST("/savefolder", touchmessagegroup.SaveFolder)     // 保存（新增、更新）文件夹
		touchMessageGroupGroup.POST("/deletefolder", touchmessagegroup.DeleteFolder) // 删除文件夹
		touchMessageGroupGroup.POST("/folderorder", touchmessagegroup.FolderOrder)   // 全量更新文件夹排序

		touchMessageGroupGroup.GET("/folderlist", touchmessagegroup.FolderList) // 文件夹列表

		touchMessageGroupGroup.POST("/savegroup", touchmessagegroup.SaveMessageGroup)           // 保存消息组
		touchMessageGroupGroup.POST("/deletegroup", touchmessagegroup.DeleteMessageGroup)       // 删除消息组
		touchMessageGroupGroup.GET("/messagegroupdetail", touchmessagegroup.MessageGroupDetail) // 获取单个消息组

		touchMessageGroupGroup.GET("/organizationtree", touchmessagegroup.OrganizationTree)       // 组织树
		touchMessageGroupGroup.GET("/allorganizationtree", touchmessagegroup.AllOrganizationTree) // 组织树
		touchMessageGroupGroup.GET("/sidelist", touchmessagegroup.SideList)                       // 侧边栏列表
		touchMessageGroupGroup.GET("/list", touchmessagegroup.List)                               // 消息页列表

		touchMessageGroupGroup.POST("/htmlbaseinfo", touchmessagegroup.HtmlBaseInfo)                           // 解析页面数据
		touchMessageGroupGroup.GET("/materiallist", touchmessagegroup.MaterialList)                            // 获取视频号列表
		touchMessageGroupGroup.POST("/updategroupavailablerange", touchmessagegroup.UpdateGroupAvailableRange) // 刷历史数据的使用范围
	}

	// 群发微信
	wxmessageGroup := assistantRouter.Group("/wxmessage")
	{
		wxmessageGroup.GET("/folderlist", wxmessage.FolderList)
		wxmessageGroup.GET("/messagegroupdetail", assistant.MessageGroupDetail)

		wxmessageGroup.POST("/sendgroupmessage", message.SendGroupMessage)
		wxmessageGroup.POST("/messagecheck", message.MessageCheck)
		mergeCourseGroup := wxmessageGroup.Group("/mergecourse")
		{
			mergeCourseGroup.GET("/sametplcourse", mergecourse.SameTplCourse)
		}

		wxmessageGroup.POST("/updatetaskstatus", message.UpdateTaskStatus)
	}

	// 章节信息
	LessonGroup := assistantRouter.Group("/lesson")
	{
		LessonGroup.GET("/getinclassjumpurl", lesson.GetInClassJumpUrl)
		LessonGroup.POST("/getcourseremark", lesson.GetCourseRemark)
		LessonGroup.GET("/getapplist", lesson.GetAppList)
	}

	// 公用接口
	PublicGroup := assistantRouter.Group("/public")
	{
		PublicGroup.GET("/checksopgray", public.CheckSopGray)
		PublicGroup.GET("/scenechecksopgray", public.SceneCheckSopGray)
		PublicGroup.GET("/checkcallgray", public.CheckCallGray)
		PublicGroup.POST("/applist", wxmass.AppList)
	}

	// 模板内容
	templateGroup := router.Group("/template")
	{
		// 团队 范围的模板
		groupPosterGroup := templateGroup.Group("/groupposter")
		{
			groupPosterGroup.POST("/add", groupposter.Add)
			groupPosterGroup.POST("/update", groupposter.Update)
			groupPosterGroup.GET("/delete", groupposter.Delete)
			groupPosterGroup.GET("/getbyid", groupposter.GetById)
			groupPosterGroup.POST("/findbypaged", groupposter.FindByPaged)
		}
		// 自定义分类 范围的模板
		// 指代：榜单模板那部分
	}

	// 拉新标签补录工具
	laxinTagFixToolGroup := router.Group("/laxintagfixtool")
	{
		laxinTagFixToolGroup.POST("/upload", laxintagfixtool.UploadLaxinTagFile)
		laxinTagFixToolGroup.GET("/gettask", laxintagfixtool.GetLaxinTagFileTask)
		laxinTagFixToolGroup.GET("/getcase", laxintagfixtool.GetLaxinTagCase)
		laxinTagFixToolGroup.GET("/testconsume", laxintagfixtool.TestConsume)
		laxinTagFixToolGroup.GET("/getchangeLog", laxintagfixtool.GetCaseChangeLog)
	}

	exerciseNoteGroup := router.Group("/exercisenote")
	{
		exerciseNoteGroup.GET("/coursetaskurl", exercisenote.CourseTaskUrl)
		exerciseNoteGroup.GET("/exportmistakedetail.csv", exercisenote.ExportMistakeDetail)
	}
	// 预到课标签
	preClassGroup := router.Group("/preclass")
	{
		preClassGroup.POST("/update", preclass.UpdateMultiPreClassTag)
		preClassGroup.POST("/detail", preclass.GetPreClassDetail)
		preClassGroup.POST("/lessonstates", preclass.GetAllLessonStates)
	}

	// 伴学
	accompanyGroup := router.Group("/accompany")
	{
		accompanyGroup.POST("/taginfo", accompany.GetTagInfo)
	}

	// 满意度收集
	satisfactionGroup := router.Group("/satisfaction")
	{
		satisfactionGroup.POST("/savetplscore", satisfaction.SaveTplScore)
	}

	bailingGroup := router.Group("/bailing")
	{
		//sop
		sopGroup := bailingGroup.Group("/sop")
		{
			sopGroup.GET("/studentcourseremark", sop.StudentCourseRemark)
		}
		//个人信息
		studentGroup := bailingGroup.Group("/student")
		{
			studentGroup.GET("/setbelonger", student.SetBelonger)
			studentGroup.GET("/getbelongermap", student.GetBelongerMap)
			studentGroup.GET("/studentdetail", student.StudentDetail)
			studentGroup.GET("/getstudentuidbywexinid", student.GetStudentUidByWxId)
		}
		//维系记录
		interviewGroup := bailingGroup.Group("interview")
		{
			interviewGroup.GET("list", interview.List)
			interviewGroup.GET("detail", interview.Detail)
			interviewGroup.POST("hidetag", interview.HideTag)
			interviewGroup.GET("demo1", interview.Demo1)
			interviewGroup.GET("demo2", interview.Demo2)
			interviewGroup.GET("demo3", interview.Demo3)
		}
		//AI 消息
		messageGroup := bailingGroup.Group("message")
		{
			messageGroup.GET("list", aimessage.List)
			messageGroup.POST("read", aimessage.Read)
			messageGroup.GET("send", aimessage.Send)
		}
	}

	relationGroup := assistantRouter.Group("/relation")
	{
		// 通用信息
		sourceGroup := relationGroup.Group("/source")
		{
			sourceGroup.GET("/getstudentcallrecordinfo", relation.GetStudentCallRecordInfo)
			sourceGroup.GET("/getlessonlist", relation.GetLessonList)
			sourceGroup.GET("/getremarklist", relation.GetRemarkList)

		}

		touchGroup := relationGroup.Group("/touch")
		{
			// 外呼
			touchGroup.GET("/callcheck", relation.CallCheck)
			touchGroup.GET("/call", relation.Call)
			// 推名片
			touchGroup.POST("/sendwxmsg/studentsmsfilter", relation.StudentSmsFilter) //筛选学员
			touchGroup.POST("/sendwxmsg/sendaddwxmsg", relation.SendAddWxMsg)         //发送名片
			// 短信
			touchGroup.GET("/getsmssendconfig", relation.GetSmsSendConfig)
			touchGroup.GET("/sendsmssingle", relation.SendSmsSingle)
		}

		clueGroup := relationGroup.Group("/clue")
		{
			clueGroup.POST("/edit/manualintention", relation.EditManualIntention)
			clueGroup.POST("/edit/manualcallstatus", relation.EditManualCallStatus)
			clueGroup.POST("/edit/manualremark", relation.EditManualRemark)
		}

		// 公海
		publicSeaGroup := relationGroup.Group("/publicsea")
		{
			publicSeaGroup.POST("/cluelist", relation.ClueList)
			publicSeaGroup.GET("/getfiltermap", relation.GetPublicSeaFilterMap)
			publicSeaGroup.GET("/retrieve", relation.Retrieve)
			publicSeaGroup.POST("/cluestatus", relation.ClueStatus)
		}

		// 私海
		privateSeaGroup := relationGroup.Group("/privatesea")
		{

			privateSeaGroup.GET("/gettransdata", relation.GetTransData) //获得转化记录
		}

		// 服务结束
		serviceend := relationGroup.Group("/serviceend")
		{
			serviceend.GET("/getfiltermap", relation.GetFilterMap) //服务结束筛选
			serviceend.POST("/recordlist", relation.GetRecordList) //服务结束
		}
	}

	// 此接口不校验选择资产
	deskViewDetail := router.Group("/deskviewdetail")
	{
		deskViewDetail.GET("/getteacherqrcode", deskviewdetail.GetTeacherQRCode) //获取老师二维码
	}

	// 系统通知运营工具
	noticeGroup := router.Group("/notice")
	{
		managerGroup := noticeGroup.Group("manager")
		{
			managerGroup.POST("/save", notice.SaveOrUpdateNotice)
			managerGroup.POST("/list", notice.List)
			managerGroup.GET("/get", notice.Get)
			managerGroup.GET("/detail", notice.Detail)
			managerGroup.POST("/delete", notice.Delete)
			managerGroup.POST("/changestatus", notice.ChangeStatus)
			managerGroup.POST("/expectednum", notice.GetExpectedNum)
			managerGroup.POST("/expectedlist", notice.GetNoticeExpectedUserList)
			managerGroup.POST("/informlist", notice.InformList)
			managerGroup.POST("/readlist", notice.Readlist)
			managerGroup.POST("/unreadlist", notice.UnReadlist)
			managerGroup.POST("/feedbacklist", notice.Feedbacklist)
		}

		statisticGroup := noticeGroup.Group("statistic")
		{
			statisticGroup.POST("/save")
		}

		userGroup := noticeGroup.Group("user")
		{
			userGroup.GET("/getunreadnoticelist", notice.GetUnReadNoticeList)
			userGroup.GET("/gettopnoticelist", notice.GetTopNoticeList)
			userGroup.GET("/getnoticedetail", notice.GetNoticeDetail)
			userGroup.POST("/feedback", notice.UserNoticeFeedback)
			userGroup.POST("/comment", notice.UserNoticeComment)
			userGroup.POST("/commentlike", notice.UserNoticeCommentLike)
			userGroup.GET("/getnoticelist", notice.UserNoticeList)
			userGroup.POST("/noticereadlater", notice.UserNoticeReadLater)
			userGroup.POST("/readtime", notice.UserNoticeReadTime)
			userGroup.POST("/readall", notice.UserNoticeReadAll)
		}

		commentGroup := noticeGroup.Group("comment")
		{
			commentGroup.POST("/getnoticelist", notice.GetNoticeListForComment)
			commentGroup.POST("/list", notice.GetCommentList)
			commentGroup.POST("/reply", notice.ReplyNoticeComment)
			commentGroup.POST("/setselected", notice.SetCommentSelected)
			commentGroup.POST("/delete", notice.DeleteComment)
		}

		commonGroup := noticeGroup.Group("common")
		{
			commonGroup.GET("/getclassdict", notice.GetClassDict)
			commonGroup.GET("/getbostoken", notice.GetBosTmpToken)
		}

		exportGroup := noticeGroup.Group("export")
		{
			exportGroup.GET("/noticelist", notice.ExportNoticeList)
			exportGroup.GET("/noticeuserlist", notice.ExportNoticeUserList)
		}
	}

	notifyIgnoreGroup := router.Group("/notifyignore")
	{
		notifyIgnoreGroup.POST("/add", notifyignore.AddNotifyIgnore)
		notifyIgnoreGroup.POST("/list", notifyignore.ListNotifyIgnore)
		notifyIgnoreGroup.POST("/remove", notifyignore.DeleteNotifyIgnore)
	}

	sceneTouch := router.Group("/scenetouch")
	{
		prepare := sceneTouch.Group("/prepare")
		{
			prepare.POST("/usejob", scenetouch.UseJob)
			prepare.POST("/jobstatus", scenetouch.JobStatus)
			prepare.POST("/jobresult", scenetouch.JobResult)
		}
	}

	leadsGroup := router.Group("/leads")
	{
		leadsGroup.Any("/layerdetailinfo", leads.GetLeadsLayerDetailInfo)
	}

	deerGroup := router.Group("/deer")
	{
		deerGroup.Any("/getevaluatetab", deer.GetEvaluateTab)
		deerGroup.Any("/getevaluatestage2report", deer.GetEvaluateStage2Report)
	}
}
