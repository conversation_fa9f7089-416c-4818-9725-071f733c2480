package deer

import (
	"assistantdeskgo/api/achilles"
	"assistantdeskgo/api/stallman"
	"assistantdeskgo/dto/dtodeer"
	"assistantdeskgo/utils"
	"fmt"
	"strings"

	"git.zuoyebang.cc/fwyybase/fwyylibs/api/su"
	"git.zuoyebang.cc/pkg/golib/v2/env"
	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

func GetEvaluateTab(ctx *gin.Context, req dtodeer.GetDeerEvaluateTabReq) (rsp dtodeer.GetDeerEvaluateTabRsp, err error) {

	courseId := req.CourseId
	studentUid := req.StudentUid

	// 根据courseId获取答题tab栏数据
	courseDetail, err := achilles.CourseDetail(ctx, achilles.CourseDetailReq{CourseId: []string{cast.ToString(courseId)}})
	if err != nil {
		return
	}

	if courseDetailInfo, ok := courseDetail.Details[cast.ToString(courseId)]; ok {
		// 存在课前测评
		if len(courseDetailInfo.BindExams) > 0 {
			stuexam, err := stallman.Stuexam(ctx, stallman.StuexamReq{CourseId: courseId, Uid: studentUid})
			if err != nil {
				return rsp, err
			}

			tabInfo := dtodeer.TabInfo{
				TabName: "课前测评",
				TabType: dtodeer.TabTypeBeforeClass,
				TabInfo: map[string]any{
					"examId": stuexam.ExamId,
				},
			}
			rsp.TabList = append(rsp.TabList, tabInfo)
		}

		// 存在课中测评
		if len(courseDetailInfo.BindLessons) > 0 {
			for i := 0; i < len(courseDetailInfo.BindLessons); i++ {
				tabInfo := dtodeer.TabInfo{
					TabName: fmt.Sprintf("第%s讲测评", utils.ToChineseNumber(i+1)),
					TabType: dtodeer.TabTypeInClass,
					TabInfo: map[string]any{
						"lessonId": courseDetailInfo.BindLessons[i].LessonId,
					},
				}
				rsp.TabList = append(rsp.TabList, tabInfo)
			}
		}
	}

	return
}

func GetEvaluateStage2Report(ctx *gin.Context, req dtodeer.GetEvaluateStage2ReportReq) (rsp dtodeer.GetEvaluateStage2ReportRsp, err error) {

	baseUrl := "https://deer.zuoyebang.com/static/learneval/eval-app/report?courseId=%d&uid=%d&from=lpc&device=mobileBrowser"
	// ship环境用
	if env.GetRunEnv() == env.RunEnvTest {
		baseUrl = "https://deer-zkx-e.suanshubang.cc/static/learneval/eval-app/report?courseId=%d&uid=%d&from=lpc&device=mobileBrowser"
	}

	url := fmt.Sprintf(baseUrl, req.CourseId, req.StudentUid)
	shortUrl, err := su.CreateShortUrl(ctx, url, su.DefaultExpireTime, true)
	if err != nil {
		return rsp, err
	}

	// 如果shortUrl没有协议，则添加https协议
	if !strings.HasPrefix(shortUrl, "http://") && !strings.HasPrefix(shortUrl, "https://") {
		rsp.Url = "https://" + shortUrl
	} else {
		rsp.Url = shortUrl
	}
	return
}
