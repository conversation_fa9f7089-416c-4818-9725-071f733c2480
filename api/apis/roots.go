package apis

import (
	"assistantdeskgo/conf"
	"git.zuoyebang.cc/pkg/golib/v2/base"
)

func init() {
	BindDefaultRoots()
	BindDefaultPaths()
}

var (
	PassportRoots      = []string{"ucloud", "session"}
	ZbCoreDatRoots     = []string{"zbcore-dat"}
	CourseSearchRoots  = []string{"coursesearch"}
	TowerRoots         = []string{"tower"}
	SuRoots            = []string{"su"}
	AssistantDeskRoots = []string{"assistantdesk"}
	AccelerateRoots    = []string{"accelerate"}
	MoatRoots          = []string{"newgoodsplatform"}
	KunPengRoots       = []string{"kpapi"}
	UserprofileRoots   = []string{"userprofile"}
	MercuryRoots       = []string{"mercury"}
	PlumRoots          = []string{"plum"}
	MuseRoots          = []string{"muse"}
	TouchmisRoots      = []string{"touchmis"}
	OfficeSeverRoots   = []string{"office-server"}
	LpcMsg             = []string{"reach"}
	FwyyEvaluateRoots  = []string{"fwyy-evaluate"}
	AiTurbo            = []string{"aiturbo"}
	MeshRoots          = []string{"mesh"}

	KunPengStaffRoots = []string{"kpstaff"}
	ClassmeRoots      = []string{"classmeinner"}
	AssistantCourseGo = []string{"assistantcoursego"}
	ExerciseRoot      = []string{"exercise-api"}
	Dataproxy         = []string{"dataproxy"}
	Allocate          = []string{"allocate"}
	ExamCore          = []string{"examcore"}
	CourseBase        = []string{"coursebase"}
	TouchmisGo        = []string{"touchmisgo"}
	Delayer           = []string{"delayer"}
	Duxuesc           = []string{"duxuesc"}
	CoursetransGo     = []string{"coursetransgo"}
	Agg               = []string{"agg"}
	Eduprobe          = []string{"eduprobe"}
	AssistantAi       = []string{"assistant-ai"}
	LaxinMis          = []string{"laxinmis"}
	MisCourse         = []string{"miscourse"}
	Achilles          = []string{"achilles"}
	Stallman          = []string{"stallman"}
	ArkGo             = []string{"arkgo"}
)

var (
	PathToClient = map[string]*base.ApiClient{}
)

var (
	tagToClient = map[string]struct {
		roots  []string
		client *base.ApiClient
	}{
		"passport":          {PassportRoots, &conf.API.Passport},
		"zbcore_dat":        {ZbCoreDatRoots, &conf.API.ZbCoreDat},
		"coursesearch":      {CourseSearchRoots, &conf.API.CourseSearch},
		"tower":             {TowerRoots, &conf.API.Tower},
		"su":                {SuRoots, &conf.API.Su},
		"assistantdesk":     {AssistantDeskRoots, &conf.API.AssistantDesk},
		"accelerate":        {AccelerateRoots, &conf.API.Accelerate},
		"moat":              {MoatRoots, &conf.API.Moat},
		"kpapi":             {KunPengRoots, &conf.API.KunPeng},
		"userprofile":       {UserprofileRoots, &conf.API.UserProfile},
		"mercury":           {MercuryRoots, &conf.API.Mercury},
		"plum":              {PlumRoots, &conf.API.Plum},
		"muse":              {MuseRoots, &conf.API.Muse},
		"touchmis":          {TouchmisRoots, &conf.API.TouchMis},
		"office-server":     {OfficeSeverRoots, &conf.API.OfficeServer},
		"reach":             {LpcMsg, &conf.API.LpcMsg},
		"fwyy-evaluate":     {FwyyEvaluateRoots, &conf.API.FwyyEvaluate},
		"mesh":              {MeshRoots, &conf.API.Mesh},
		"classme":           {ClassmeRoots, &conf.API.ClassMe},
		"aiTurbo":           {AiTurbo, &conf.API.AiTurbo},
		"kpStaff":           {KunPengStaffRoots, &conf.API.KpStaff},
		"assistantCourseGo": {AssistantCourseGo, &conf.API.AssistantcourseGo},
		"exercise-api":      {ExerciseRoot, &conf.API.Exercise},
		"dataproxy":         {Dataproxy, &conf.API.DataProxy},
		"allocate":          {Allocate, &conf.API.Allocate},
		"examcore":          {ExamCore, &conf.API.ExamCore},
		"coursebase":        {CourseBase, &conf.API.CourseBase},
		"touchmisgo":        {TouchmisGo, &conf.API.TouchmisGo},
		"delayer":           {Delayer, &conf.API.Delayer},
		"duxuesc":           {Duxuesc, &conf.API.Duxuesc},
		"coursetransgo":     {CoursetransGo, &conf.API.CoursetransGo},
		"agg":               {Agg, &conf.API.Agg},
		"eduprobe":          {Eduprobe, &conf.API.Eduprobe},
		"assistant-ai":      {AssistantAi, &conf.API.AssistantAi},
		"laxinmis":          {LaxinMis, &conf.API.Laxinmis},
		"miscourse":         {MisCourse, &conf.API.MisCourse},
		"stallman":          {Stallman, &conf.API.Stallman},
		"achilles":          {Achilles, &conf.API.AchillesV3Server},
		"arkgo":             {ArkGo, &conf.API.ArkGo},
	}
)

func BindDefaultRoots() {
	for _, to := range tagToClient {
		bindPathRootsToClient(to.roots, to.client)
	}
}

func BindDefaultPaths() {
	for k, v := range PathToClient {
		bindPathToClient(k, v)
	}
}
