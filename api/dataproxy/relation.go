package dataproxy

import (
	"assistantdeskgo/api/apis"
	"assistantdeskgo/utils"
	"git.zuoyebang.cc/pkg/golib/v2/zlog"
	"github.com/gin-gonic/gin"
)

const (
	apiClueList = "/dataproxy/relation/publicsea/cluelist"

	StatusValid   = 1
	StatusDeleted = 2
	StatusInvalid = 3
	StatusBack    = 4 // 会公海

	LockStatusOff = 0
	LockStatusOn  = 1

	SelectTypeList  SelectType = 0 // 只查询list
	SelectTypeTotal SelectType = 1 // 只查询total
	SelectTypeAll   SelectType = 2 // total和list都查询
)

type SelectType = int64

type ClueListReq struct {
	Fields     []string               `json:"fields"` // 不传查所有列
	Filter     map[string]interface{} `json:"filter"`
	OrClueIds  []string               `json:"orClueIds"`  // filter的或条件查询 filter || in orClueIds
	ExClueIds  []string               `json:"exClueIds"`  // filter的或条件查询 filter || not in exClueIds
	SelectType int64                  `json:"selectType"` // 0=查list，1=查total，2=查list和total
	Pn         int64                  `json:"pn"`
	Rn         int64                  `json:"rn"`
}

type ClueListResp struct {
	List  []ClueInfo `json:"list" mapstructure:"list"`
	Total int64      `json:"total" mapstructure:"total"`
}

type ClueInfo struct {
	ClueId         string `json:"clue_id" mapstructure:"clue_id"`
	StudentUid     int64  `json:"student_uid" mapstructure:"student_uid"`
	CourseId       int64  `json:"course_id" mapstructure:"course_id"`
	PhoneAfter     string `json:"phone_after" mapstructure:"phone_after"`
	Batch          string `json:"batch" mapstructure:"batch"`
	OrgId          string `json:"org_id" mapstructure:"org_id"`
	Status         int64  `json:"status" mapstructure:"status"`
	LockStatus     int64  `json:"lock_status" mapstructure:"lock_status"`
	MainGradeId    int64  `json:"main_grade_id" mapstructure:"main_grade_id"`
	MainSubjectId  int64  `json:"main_subject_id" mapstructure:"main_subject_id"`
	YearSeason     string `json:"year_season" mapstructure:"year_season"`
	SeasonSemester string `json:"season_semester" mapstructure:"season_semester"`
	LastFrom       string `json:"last_from" mapstructure:"last_from"`
	AttendTimes    int64  `json:"attend_times" mapstructure:"attend_times"`
	FinishTimes    int64  `json:"finish_times" mapstructure:"finish_times"`
	ProvinceName   string `json:"province_name" mapstructure:"province_name"`
	CityName       string `json:"city_name" mapstructure:"city_name"`
	CityLevel      int64  `json:"city_level" mapstructure:"city_level"`
	UserType       int64  `json:"user_type" mapstructure:"user_type"`
	ExpireTime     int64  `json:"expire_time" mapstructure:"expire_time"`
	CreateTime     int64  `json:"create_time" mapstructure:"create_time"`
	UpdateTime     int64  `json:"update_time" mapstructure:"update_time"`
}

func getClueListEsFields() []string {
	return []string{
		"clue_id",
		"student_uid",
		"course_id",
		"phone_after",
		"batch",
		"org_id",
		"status",
		"lock_status",
		"main_grade_id",
		"main_subject_id",
		"year_season",
		"season_semester",
		"last_from",
		"attend_times",
		"finish_times",
		"province_name",
		"city_name",
		"city_level",
		"user_type",
		"expire_time",
		"create_time",
		"update_time",
	}
}

func ClueList(ctx *gin.Context, req ClueListReq) (resp ClueListResp, err error) {
	if len(req.Fields) == 0 {
		req.Fields = getClueListEsFields()
	}
	err = apis.Do(ctx, req, &resp, apis.WithCookies(utils.GetCookieMap(ctx)))
	zlog.Debugf(ctx, "ClueList.ral.req: %+v, resp: %+v", req, resp)
	if err != nil {
		zlog.Warnf(ctx, "ClueList.ral failed, req:%+v, err:%+v", req, err)
		return
	}
	return
}
