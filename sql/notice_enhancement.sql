-- 通知系统功能增强 - 数据库表结构变更
-- 功能一：通知头像自定义支持
-- 功能二：轮播位样式升级

-- 为 tblSystemNotice 表添加新字段
ALTER TABLE tblSystemNotice 
ADD COLUMN avatar_type VARCHAR(20) NOT NULL DEFAULT '' COMMENT '头像类型：gradient，solid，border',
ADD COLUMN avatar_color VARCHAR(20) NOT NULL DEFAULT '' COMMENT '头像颜色',
ADD COLUMN cover_image VARCHAR(500) NOT NULL DEFAULT '' COMMENT '封面',
ADD COLUMN bottom_image_id INT NOT NULL DEFAULT 0 COMMENT '底图',
ADD COLUMN schedule_pub_time INT NOT NULL DEFAULT 0 COMMENT '定时发布时间',
ADD COLUMN schedule_end_time INT NOT NULL DEFAULT 0 COMMENT '定时结束时间',
ADD COLUMN blacklist_group Text COMMENT '黑名单组织',
ADD COLUMN blacklist_uid Text COMMENT '黑名单uid';

ALTER TABLE tblSystemNoticeUser
ADD COLUMN first_read_time INT NOT NULL DEFAULT 0 COMMENT '首次浏览时间',
ADD COLUMN exposure_time INT NOT NULL DEFAULT 0 COMMENT '曝光时间';

CREATE TABLE `tblSystemNoticeShare` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `notice_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通知id',
  `creator_uid` bigint(20) NOT NULL COMMENT '分享人uid',
  `share_uuid` varchar(255) NOT NULL COMMENT '分享uuid',
  `read_num` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '阅读次数',
  `create_time` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '创建时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统通知分享记录';