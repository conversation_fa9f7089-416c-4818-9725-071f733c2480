package utils

import (
	"github.com/gin-gonic/gin"
)

// GetZYBUSS 获取Cookie中的ZYBUSS
func GetZYBUSS(ctx *gin.Context) (zybuss string, err error) {
	_ = ctx.Request.ParseForm()
	zybuss, err = ctx.Cookie("zybuss")
	if err != nil || zybuss == "" {
		zybuss, err = ctx.Cookie("ZYBUSS")
		if err != nil || zybuss == "" {
			zybuss = ctx.Request.Form.Get("ZYBUSS")
			if zybuss == "" {
				return
			}
		}
	}
	return
}

func GetCookieMap(ctx *gin.Context) (cookie map[string]string) {
	cookie = make(map[string]string)
	if ctx == nil || ctx.Request == nil || len(ctx.Request.Cookies()) == 0 {
		return
	}

	for _, ck := range ctx.Request.Cookies() {
		cookie[ck.Name] = ck.Value
	}
	return
}
